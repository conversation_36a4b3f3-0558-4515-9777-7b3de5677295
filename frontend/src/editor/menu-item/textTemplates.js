// 字体模板配置
export const getTextResources = (t) => [
  // 标题类
  {
    name: t("text_title"),
    fontSize: 68,
    fontWeight: 800,
    fontFamily: "Arial Black",
    category: "title",
    color: "#000000",
    description: "电影级别的主标题，庄重大气",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: undefined,
    strokeWidth: 1,
    strokeColor: "#cccccc",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 0.95,
    charSpacing: 3,
  },
  {
    name: t("text_subtitle"),
    fontSize: 36,
    fontWeight: 600,
    fontFamily: "Roboto",
    category: "title",
    color: "#000000",
    description: t("text_desc_subtitle"),
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.1,
    charSpacing: 0,
  },

  {
    name: "Chapter",
    fontSize: 44,
    fontWeight: 700,
    fontFamily: "Arial",
    category: "title",
    color: "#ffd700",
    description: "清晰的章节分割标题",
    shadowBlur: 0,
    shadowOffsetX: 2,
    shadowOffsetY: 2,
    shadowColor: "rgba(0,0,0,0.7)",
    strokeWidth: 1,
    strokeColor: "#ffffff",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.1,
    charSpacing: 2,
  },
  {
    name: "Brand",
    fontSize: 52,
    fontWeight: 600,
    fontFamily: "Open Sans",
    category: "title",
    color: "#2196f3",
    description: "现代感品牌标题",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 4,
    shadowColor: "#2196F3",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.0,
    charSpacing: 1,
  },
  // 字幕类
  {
    name: "Standard",
    fontSize: 30,
    fontWeight: 500,
    fontFamily: "Arial",
    category: "subtitle",
    color: "#ffffff",
    description: "清晰易读的通用字幕",
    shadowBlur: 0,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    shadowColor: "#222222",
    strokeWidth: 1,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.3,
    charSpacing: 0,
  },
  {
    name: "Cinema",
    fontSize: 40,
    fontWeight: 400,
    fontFamily: "Lato",
    category: "subtitle",
    color: "#ffffff",
    description: "电影院风格字幕",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "#000000",
    textAlign: "center",
    lineHeight: 1.4,
    charSpacing: 0,
  },
  {
    name: "News",
    fontSize: 28,
    fontWeight: 600,
    fontFamily: "Arial",
    category: "subtitle",
    color: "#ffffff",
    description: "新闻播报专用字幕",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "#1565c0",
    textAlign: "center",
    lineHeight: 1.2,
    charSpacing: 0,
  },
  {
    name: "Documentary",
    fontSize: 24,
    fontWeight: 400,
    fontFamily: "Georgia",
    category: "subtitle",
    color: "#f8f8f8",
    description: "纪录片专业字幕",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 2,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.5,
    charSpacing: 0,
  },
  {
    name: "Bilingual",
    fontSize: 22,
    fontWeight: 500,
    fontFamily: "Arial",
    category: "subtitle",
    color: "#ffffff",
    description: "中英双语字幕样式",
    shadowBlur: 0,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "#000000",
    textAlign: "center",
    lineHeight: 1.3,
    charSpacing: 0,
  },
  // 正文类
  {
    name: t("text_body"),
    fontSize: 24,
    fontWeight: 400,
    fontFamily: "Roboto",
    category: "body",
    color: "#ffffff",
    description: t("text_desc_body"),
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.4,
    charSpacing: 0,
  },
  {
    name: t("text_caption"),
    fontSize: 18,
    fontWeight: 300,
    fontFamily: "Roboto",
    category: "body",
    color: "#ffffff",
    description: t("text_desc_caption"),
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.3,
    charSpacing: 0,
  },
  {
    name: "Educational",
    fontSize: 20,
    fontWeight: 500,
    fontFamily: "Roboto",
    category: "body",
    color: "#2196f3",
    description: "教育视频中的说明文字",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 1,
    shadowColor: "#ffffff",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "#ffffff",
    textAlign: "center",
    lineHeight: 1.4,
    charSpacing: 0,
  },
  // 标注类
  {
    name: "Highlight",
    fontSize: 34,
    fontWeight: 700,
    fontFamily: "Roboto",
    category: "annotation",
    color: "#ff3030",
    description: "突出重要信息的标注",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 2,
    shadowColor: "#000000",
    strokeWidth: 2,
    strokeColor: "#ffffff",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.0,
    charSpacing: 1,
  },
  {
    name: "Warning",
    fontSize: 26,
    fontWeight: 600,
    fontFamily: "Arial",
    category: "annotation",
    color: "#ffffff",
    description: "警告和提示标签",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "#ff9800",
    textAlign: "center",
    lineHeight: 1.2,
    charSpacing: 0,
  },
  {
    name: "Location",
    fontSize: 20,
    fontWeight: 500,
    fontFamily: "Roboto",
    category: "annotation",
    color: "#ffffff",
    description: "地点和位置标记",
    shadowBlur: 0,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "#4caf50",
    textAlign: "center",
    lineHeight: 1.1,
    charSpacing: 0,
  },
  {
    name: "Timestamp",
    fontSize: 22,
    fontWeight: 400,
    fontFamily: "Courier New",
    category: "annotation",
    color: "#ffd54f",
    description: "时间信息显示",
    shadowBlur: 0,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "#000000",
    textAlign: "center",
    lineHeight: 1.0,
    charSpacing: 1,
  },
  {
    name: "Description",
    fontSize: 18,
    fontWeight: 500,
    fontFamily: "Helvetica",
    category: "annotation",
    color: "#333333",
    description: "解释和说明文字",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "#ffffff",
    textAlign: "center",
    lineHeight: 1.3,
    charSpacing: 0,
  },
  // 社交媒体类
  {
    name: "TikTok",
    fontSize: 44,
    fontWeight: 800,
    fontFamily: "Arial Black",
    category: "social",
    color: "#ff0050",
    description: "抖音风格动感标题",
    shadowBlur: 0,
    shadowOffsetX: 3,
    shadowOffsetY: 3,
    shadowColor: "#00f2ea",
    strokeWidth: 1,
    strokeColor: "#ffffff",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 0.95,
    charSpacing: 2,
  },
  {
    name: "Bilibili",
    fontSize: 40,
    fontWeight: 700,
    fontFamily: "Arial",
    category: "social",
    color: "#ffffff",
    description: "B站风格标题",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 3,
    shadowColor: "#000000",
    strokeWidth: 2,
    strokeColor: "#00a1d6",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.0,
    charSpacing: 1,
  },
  {
    name: "YouTube",
    fontSize: 46,
    fontWeight: 900,
    fontFamily: "Roboto",
    category: "social",
    color: "#ffffff",
    description: "YouTube缩略图专用",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 3,
    strokeColor: "#000000",
    backgroundColor: "#ff0000",
    textAlign: "center",
    lineHeight: 0.9,
    charSpacing: 1,
  },
  {
    name: "Weibo",
    fontSize: 34,
    fontWeight: 600,
    fontFamily: "Arial",
    category: "social",
    color: "#ff6900",
    description: "微博热搜标题风格",
    shadowBlur: 0,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "#ffffff",
    textAlign: "center",
    lineHeight: 1.2,
    charSpacing: 0,
  },
  {
    name: "Instagram",
    fontSize: 42,
    fontWeight: 700,
    fontFamily: "Helvetica",
    category: "social",
    color: "#ffffff",
    description: "时尚的Instagram快拍文字",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 2,
    shadowColor: "#333333",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.1,
    charSpacing: 1,
  },
  {
    name: "Twitter",
    fontSize: 32,
    fontWeight: 700,
    fontFamily: "Helvetica",
    category: "social",
    color: "#ffffff",
    description: "简洁有力的Twitter (X) 风格",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "#000000",
    textAlign: "center",
    lineHeight: 1.3,
    charSpacing: 0,
  },
  {
    name: "Facebook",
    fontSize: 36,
    fontWeight: 600,
    fontFamily: "Open Sans",
    category: "social",
    color: "#1877F2",
    description: "经典的Facebook蓝标题",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 1,
    shadowColor: "#DDDDDD",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.2,
    charSpacing: 0,
  },
  {
    name: "Reddit",
    fontSize: 34,
    fontWeight: 700,
    fontFamily: "Verdana",
    category: "social",
    color: "#ffffff",
    description: "Reddit社区风格标题",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "#FF4500",
    textAlign: "center",
    lineHeight: 1.2,
    charSpacing: 0,
  },
  // 创意字体类
  {
    name: "Creative",
    fontSize: 48,
    fontWeight: 700,
    fontFamily: "Impact",
    category: "creative",
    color: "#ff6b35",
    description: t("text_desc_creative_title"),
    shadowBlur: 0,
    shadowOffsetX: 2,
    shadowOffsetY: 2,
    shadowColor: "#000000",
    strokeWidth: 2,
    strokeColor: "#ffffff",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.1,
    charSpacing: 2,
  },
  {
    name: "Elegant",
    fontSize: 42,
    fontWeight: 400,
    fontFamily: "Georgia",
    category: "creative",
    color: "#8e44ad",
    description: t("text_desc_elegant_title"),
    shadowBlur: 0,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.2,
    charSpacing: 1,
  },
  {
    name: "Tech",
    fontSize: 32,
    fontWeight: 500,
    fontFamily: "Courier New",
    category: "creative",
    color: "#00d4aa",
    description: t("text_desc_tech"),
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.0,
    charSpacing: 3,
  },
  {
    name: "Classic",
    fontSize: 38,
    fontWeight: 400,
    fontFamily: "Georgia",
    category: "creative",
    color: "#2c3e50",
    description: t("text_desc_classic_serif"),
    shadowBlur: 0,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    shadowColor: "#666666",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.3,
    charSpacing: 0,
  },
  {
    name: "Elegant",
    fontSize: 34,
    fontWeight: 400,
    fontFamily: "Georgia",
    category: "creative",
    color: "#333333",
    description: "经典衬线字体，优雅正式",
    shadowBlur: 0,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.3,
    charSpacing: 0,
  },
  {
    name: "Modern",
    fontSize: 46,
    fontWeight: 300,
    fontFamily: "Helvetica",
    category: "creative",
    color: "#007BFF",
    description: "简洁清晰的现代感字体",
    shadowBlur: 0,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.2,
    charSpacing: 1,
  },
  // 特效样式类
  {
    name: "Neon",
    fontSize: 44,
    fontWeight: 600,
    fontFamily: "Arial",
    category: "effects",
    color: "#00ffff",
    description: "Neon Glow",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#00ffff",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.1,
    charSpacing: 4,
  },
  {
    name: "Retro",
    fontSize: 40,
    fontWeight: 700,
    fontFamily: "Impact",
    category: "effects",
    color: "#ff4757",
    description: "Retro Style",
    shadowBlur: 0,
    shadowOffsetX: 3,
    shadowOffsetY: 3,
    shadowColor: "#8B0000",
    strokeWidth: 3,
    strokeColor: "#ffffff",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.0,
    charSpacing: 2,
  },
  {
    name: "Modern",
    fontSize: 36,
    fontWeight: 300,
    fontFamily: "Arial",
    category: "effects",
    color: "#ffffff",
    description: "Modern Clean",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 2,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "#2f3542",
    textAlign: "center",
    lineHeight: 1.2,
    charSpacing: 1,
  },
  {
    name: "Playful Fun",
    fontSize: 46,
    fontWeight: 400,
    fontFamily: "Verdana",
    category: "effects",
    color: "#ff9ff3",
    description: "活泼有趣的风格",
    shadowBlur: 0,
    shadowOffsetX: 2,
    shadowOffsetY: 2,
    shadowColor: "#ff1493",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.1,
    charSpacing: 1,
  },
  {
    name: "3D Effect",
    fontSize: 48,
    fontWeight: 800,
    fontFamily: "Arial Black",
    category: "effects",
    color: "#ffffff",
    description: "醒目的3D立体文字",
    shadowBlur: 0,
    shadowOffsetX: 3,
    shadowOffsetY: 3,
    shadowColor: "#555555",
    strokeWidth: 2,
    strokeColor: "#FAFAFA",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 0.9,
    charSpacing: 2,
  },
  {
    name: "Fire Effect",
    fontSize: 46,
    fontWeight: 700,
    fontFamily: "Arial Black",
    category: "effects",
    color: "#ff4500",
    description: "炽热火焰燃烧效果",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#ff0000",
    strokeWidth: 1,
    strokeColor: "#ffff00",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.0,
    charSpacing: 3,
  },
  {
    name: "Ice Effect",
    fontSize: 42,
    fontWeight: 600,
    fontFamily: "Helvetica",
    category: "effects",
    color: "#87ceeb",
    description: "冰冷霜雪文字效果",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#ffffff",
    strokeWidth: 2,
    strokeColor: "#b0e0e6",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.0,
    charSpacing: 2,
  },
  {
    name: "Metal Effect",
    fontSize: 40,
    fontWeight: 700,
    fontFamily: "Arial Black",
    category: "effects",
    color: "#c0c0c0",
    description: "金属光泽反射效果",
    shadowBlur: 0,
    shadowOffsetX: 2,
    shadowOffsetY: 2,
    shadowColor: "#404040",
    strokeWidth: 1,
    strokeColor: "#ffffff",
    backgroundColor: undefined,
    textAlign: "center",
    lineHeight: 1.0,
    charSpacing: 1,
  },
];
