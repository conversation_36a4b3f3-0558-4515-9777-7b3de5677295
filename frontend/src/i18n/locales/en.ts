// English translations
const enTranslations = {
  // Common
  theme: "Theme",
  language: "Language",
  light_mode: "Light Mode",
  dark_mode: "Dark Mode",
  settings: "Settings",
  chinese: "Chinese",
  english: "English",
  text: "Text",

  // Navbar
  undo: "Undo",
  redo: "Redo",
  export: "Export",
  shortcuts: "Shortcuts",
  keyboard_shortcuts: "Keyboard Shortcuts",
  video_editor_logo: "Video Editor Logo",

  // Editing
  move_mode: "Move Mode",
  hand_tool: "Hand Tool",
  edit_mode: "Edit Mode",
  zoom_in: "Zoom In",
  zoom_out: "Zoom Out",
  fit_to_screen: "Fit to Screen",

  // Project
  untitled_project: "Untitled Project",
  save_project: "Save Project",
  project_name: "Project Name",
  untitled_video: "Untitled Video",

  // Export
  export_video: "Export Video",
  export_format: "Export Format",
  export_quality: "Export Quality",
  export_settings: "Export Settings",
  start_export: "Start Export",
  preparing_export: "Preparing export...",
  cancel: "Cancel",
  download_video: "Download Video",
  retry: "Retry",
  restart: "Restart",

  // Video Processing Status
  generating_video: "Generating video...",
  generation_complete: "Generation Complete",
  generation_failed: "Generation Failed",
  cancelled: "Cancelled",

  // Video Processing Stages
  initializing: "Initializing",
  detecting_audio: "Detecting Audio",
  detecting_audio_elements: "Analyzing Audio Elements",
  processing_elements: "Processing Elements",
  processing_audio_elements: "Processing Audio",
  processing_visual_elements: "Processing Visual Elements",
  processing_captions: "Processing Captions",
  building_command: "Building Command",
  generating_command: "Generating Command",
  rendering: "Rendering",
  finalizing: "Finalizing",
  completed: "Completed",
  failed: "Failed",

  // Loading
  loading: "Loading...",
  processing: "Processing...",

  // Shortcut Categories
  category_edit: "Edit",
  category_tools: "Tools",
  category_view: "View",
  category_project: "Project",
  category_help: "Help",
  category_playback: "Playback",

  // Shortcut Descriptions
  shortcut_undo: "Undo",
  shortcut_redo: "Redo",
  shortcut_move_mode: "Move Mode",
  shortcut_hand_tool: "Hand Tool",
  shortcut_zoom_in: "Zoom In",
  shortcut_zoom_out: "Zoom Out",
  shortcut_fit_to_screen: "Fit to Screen",
  shortcut_save_project: "Save Project",
  shortcut_export_video: "Export Video",
  shortcut_show_shortcuts: "Show Shortcuts",
  shortcut_delete_selected: "Delete Selected Element",
  shortcut_duplicate_selected: "Duplicate Selected Element",
  shortcut_play_pause: "Play/Pause",

  // Video Formats
  format_mp4: "MP4 Video",
  format_gif: "GIF Animation",
  format_mov: "MOV Video",

  // Video Aspect Ratios
  ratio: "Ratio",
  original: "Original",
  aspect_ratio_16_9: "16:9",
  aspect_ratio_4_3: "4:3",
  aspect_ratio_2_1: "2:1",
  aspect_ratio_9_16: "9:16",
  aspect_ratio_1_1: "1:1",
  aspect_ratio_3_4: "3:4",

  // Video Background
  background: "Background",
  recents: "Recents",
  recommended: "Recommended",
  transparent: "Transparent",
  no_background: "No Background",

  // Base Settings
  no_element_selected: "No element selected",
  alignment: "Alignment",
  position: "Position",
  lock: "Lock",
  unlock: "Unlock",
  opacity: "Opacity",
  clone: "Clone",
  delete: "Delete",
  fullscreen: "Fullscreen",
  flip_horizontal: "Flip Horizontal",
  flip_vertical: "Flip Vertical",
  align_left: "Align Left",
  align_center: "Align Center",
  align_right: "Align Right",
  justify: "Justify",
  align_top: "Align Top",
  align_middle: "Align Middle",
  align_bottom: "Align Bottom",
  position_x: "Position X",
  position_y: "Position Y",
  width: "Width",
  height: "Height",
  rotation: "Rotation",

  // Canvas Settings
  canvas_settings: "Canvas Settings",
  basic: "Basic",
  advanced: "Advanced",
  random_color: "Random Color",
  gradient: "Gradient",
  duration: "Duration",
  seconds: "seconds",
  import: "Import",

  // Font Settings
  font_setting: "Font Setting",
  font_family: "Font Family",
  font_size: "Font Size",
  font_color: "Font Color",
  text_align: "Text Align",
  styles: "Styles",
  char_spacing: "Character Spacing",
  line_height: "Line Height",
  bold: "Bold",
  italic: "Italic",
  underline: "Underline",
  strikethrough: "Strikethrough",
  stroke_width: "Stroke Width",
  stroke_color: "Stroke Color",
  shadow_color: "Shadow Color",
  shadow_blur: "Shadow Blur",
  shadow_offset_x: "Shadow Offset X",
  shadow_offset_y: "Shadow Offset Y",
  use_gradient: "Use Gradient",

  // Image Settings
  image_settings: "Image Settings",
  crop_image: "Crop Image",
  apply_crop: "Apply Crop",
  cancel_crop: "Cancel Crop",
  show_settings: "Show Settings",
  hide_settings: "Hide Settings",
  border_width: "Border Width",
  border_color: "Border Color",
  border_style: "Border Style",
  border_radius: "Border Radius",
  solid: "Solid",
  dashed: "Dashed",
  dotted: "Dotted",
  effects: "Effects",
  brightness: "Brightness",
  contrast: "Contrast",
  saturation: "Saturation",
  hue: "Hue",
  blur: "Blur",
  reset_filters: "Reset Filters",

  // Video Settings
  video_settings: "Video Settings",
  crop_video: "Crop Video",
  playback_speed: "Playback Speed",
  volume: "Volume",
  mute: "Mute",
  video: "Video",

  // Effect Types
  none: "None",
  black_and_white: "Black and White",
  saturate: "Saturate",
  sepia: "Sepia",
  invert: "Invert",

  // Shape Settings
  shape_properties: "Shape Properties",
  shape_type: "Shape Type",
  rectangle: "Rectangle",
  rounded_rectangle: "Rounded Rectangle",
  circle: "Circle",
  ellipse: "Ellipse",
  triangle: "Triangle",
  line: "Line",
  pentagon: "Pentagon",
  hexagon: "Hexagon",
  octagon: "Octagon",
  parallelogram: "Parallelogram",
  arch: "Arch",
  fill_color: "Fill Color",
  background_color: "Background Color",

  // Filter Settings
  filters: "Filters",
  border_settings: "Border Settings",
  color: "Color",
  style: "Style",
  radius: "Radius",
  effect_type: "Effect Type",
  presets: "Presets",
  default: "Default",
  warm: "Warm",
  cool: "Cool",
  vintage: "Vintage",
  sharp: "Sharp",
  soft: "Soft",
  custom: "Custom",
  adjust: "Adjust",

  // Animation Settings
  animations: "Animations",
  animation_in: "In",
  animation_out: "Out",
  fade: "Fade",
  slide_down: "Slide Down",
  slide_up: "Slide Up",
  slide_left: "Slide Left",
  slide_right: "Slide Right",
  wipe_down: "Wipe Down",
  wipe_up: "Wipe Up",
  wipe_left: "Wipe Left",
  wipe_right: "Wipe Right",
  breathe: "Breathe",
  direction: "Direction",
  left: "Left",
  right: "Right",
  top: "Top",
  bottom: "Bottom",
  use_mask: "Use Mask",

  // Menu Items
  uploads: "Uploads",
  layers: "Layers",
  Video: "Video",
  Audio: "Audio",
  Image: "Image",
  Text: "Text",
  Caption: "Caption",
  Shape: "Shape",

  // Elements Panel
  move_up: "Move Up",
  move_down: "Move Down",
  move_to_top: "Move to Top",
  move_to_bottom: "Move to Bottom",

  // Uploads
  your_media: "Your media",
  project: "Project",
  workspace: "Workspace",
  upload_media: "Upload Media",
  upload_media_description: "Upload videos, images, or audio files",
  upload_to_workspace: "Upload to Workspace",
  upload_workspace_description: "Drag and drop files here or click to browse",
  workspace_assets_available:
    "Workspace assets will be available across projects",

  // Text Resources
  text_title: "Title",
  text_subtitle: "Subtitle",
  text_body: "Body",
  text_caption: "Caption",

  // Text Style Categories
  text_category_title: "Title Styles",
  text_category_subtitle: "Subtitle Styles",
  text_category_body: "Body Styles",
  text_category_annotation: "Annotation Styles",
  text_category_social: "Social Media",
  text_category_creative: "Creative Styles",

  // Text Style Descriptions
  text_desc_main_title: "Main Title",
  text_desc_subtitle: "Subtitle",
  text_desc_body: "Body Text",
  text_desc_caption: "Caption Text",
  text_desc_creative_title: "Comic Style",
  text_desc_elegant_title: "Handwriting Style",
  text_desc_tech: "Monospace Font",
  text_desc_classic_serif: "Classic Serif",

  // Text Interface
  text_search_placeholder: "Search text styles...",
  text_no_results: "No matching text styles found",
  text_try_different_keywords: "Try using different keywords",
  text_category_effects: "Effect Styles",

  // Image Library
  image_library: "Image Library",
  search_images: "Search images...",
  no_images_found: "No images found. Try another search term.",
  loading_images_failed:
    "Failed to load images from {0}, please check your network connection or try again later",
  no_images_found_try_another:
    "No images found, please try another search term or tag",
  displayed_results: "Displayed {0} / {1} results",
  unknown: "Unknown",

  // Video Library
  video_library: "Video Library",
  search_videos: "Search videos...",
  no_videos_found: "No videos found. Try a different search term.",
  loading_videos_failed:
    "Failed to load videos from {0}, please check your network connection or try again later",
  no_videos_found_try_another:
    "No videos found, please try another search term or tag",
  displayed_all_results: "Displayed all {0} results",
  video_loading_timeout: "Video loading timeout",
  video_loading_failed: "Failed to load video",

  // Audio Library
  audio_library: "Audio Library",
  local_audio: "Local Audio",
  search_placeholder: "Search...",
  no_local_audio: "Local audio library is empty.",
  no_music_found: "No music found. Try searching or selecting different tags.",
  audio_loading_failed: "Audio loading failed, please try another audio",
  all_results_displayed: "Displayed all {0} results",
  loading_popular_failed:
    "Failed to load popular tracks. Please try again later.",
  loading_jamendo_failed:
    "Failed to load Jamendo music. Please check your network connection or API key.",
  no_tag_music_found: "No music found for the selected tags.",
  tag_loading_failed: "Failed to load music by tags. Please try again later.",
  search_failed: "Failed to search Jamendo music. Please try again later.",
  no_search_result: "No music found matching your search query.",

  // Captions
  captions: "Captions",
  add_caption: "Add Caption",
  clear_all_captions: "Clear All Captions",
  clear_confirm_title: "Clear All Captions",
  clear_confirm_message:
    "Are you sure you want to delete all captions? This action cannot be undone.",
  clear_all: "Clear All",
  no_text: "No text",
  upload_srt: "Upload SRT file",
  export_captions: "Export captions",
  insert_caption: "Insert caption",
  merge_captions: "Merge captions",
  play_segment: "Play this segment",
  delete_caption: "Delete caption",
  format_should_be: "Format should be HH:MM:SS",
  invalid_time: "Invalid time value",
  start_time_less: "Start time must be less than end time",
  end_time_greater: "End time must be greater than start time",
  time_overlaps: "Time overlaps with adjacent captions",
  suggested_time: "Suggested time {0} for {1} time",
  start_time: "start",
  end_time: "end",

  // Timeline Components
  timeline_edit: "Edit",
  timeline_copy: "Copy",
  timeline_cut: "Cut",
  timeline_delete: "Delete",
  timeline_preview: "Preview",
  timeline_adjust_style: "Adjust Style",
  timeline_auto_translate: "Auto Translate",
  timeline_start: "Start",
  timeline_end: "End",

  // SeekPlayer component
  seekplayer_delete: "Delete",
  seekplayer_split_element: "Split Element",
  seekplayer_seek_backward: "Seek Backward",
  seekplayer_seek_forward: "Seek Forward",
  seekplayer_play: "Play",
  seekplayer_pause: "Pause",
  seekplayer_play_pause: "Play/Pause",
  seekplayer_zoom_in: "Zoom In",
  seekplayer_zoom_out: "Zoom Out",
  seekplayer_fit_view: "Fit to View",
};

export default enTranslations;
